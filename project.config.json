{"setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": false, "enhance": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false, "minifyWXML": true, "compileWorklet": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "appid": "touristappid", "editorSetting": {}, "libVersion": "2.31.0", "miniprogramRoot": "./"}